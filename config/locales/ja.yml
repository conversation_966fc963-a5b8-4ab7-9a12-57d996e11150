#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
ja:
  hello: 'こんにちは世界'
  messages:
    reset_password_success: やりましたね! パスワードのリセットリクエストが成功しました。手順についてはメールを確認してください。
    reset_password_failure: メールアドレスが見つかりませんでした。
    inbox_deletetion_response: 受信トレイの削除リクエストは、しばらくしてから処理されます。
  errors:
    validations:
      presence: 空白にはできません。
    webhook:
      invalid: 無効なイベントです。
    signup:
      disposable_email: 使い捨てメールは許可されません
      blocked_domain: このドメインは許可されていません。これが間違いであると思われる場合は、サポートに連絡してください。
      invalid_email: 無効なメールアドレスを入力しました。
      email_already_exists: 'あなたは既に %{email} でアカウントにサインアップしています'
      invalid_params: '無効な値が入力されました。確認のうえ再度お試しください。'
      failed: サインアップに失敗しました
    data_import:
      data_type:
        invalid: 無効なデータ型。
    contacts:
      import:
        failed: ファイルが空です。
      export:
        success: ファイルを表示する準備ができたら通知します。
      email:
        invalid: 無効なEメールです
      phone_number:
        invalid: e164形式である必要があります
    categories:
      locale:
        unique: カテゴリとポータルで一意である必要があります
    dyte:
      invalid_message_type: '無効なメッセージタイプです。アクションは許可されていません'
    slack:
      invalid_channel_id: '無効なSlackチャンネルです。もう一度お試しください。'
    inboxes:
      imap:
        socket_error: ネットワーク接続、IMAPアドレスを確認の上、再度お試しください。
        no_response_error: IMAPの資格情報を確認の上、再度お試しください。
        host_unreachable_error: ホストに接続できません。IMAPアドレス、ポートを確認の上、再度お試しください。
        connection_timed_out_error: '%{address}:%{port} への接続がタイムアウトしました'
        connection_closed_error: 接続が閉じられました。
      validations:
        name: 記号で開始または終了しないでください。< > / \ @ を使用しないでください。
    custom_filters:
      number_of_records: 制限に達しました。1つのアカウントにつき、ユーザーごとに許可されるカスタムフィルターの最大数は 50 です。
      invalid_attribute: 無効な属性キー - [%{key}]。キーは[%{allowed_keys}]のいずれかである必要があります。または、アカウント内で定義されたカスタム属性でなければなりません。
      invalid_operator: 無効な演算子です。%{attribute_name} に許可されている演算子は [%{allowed_keys}] です。
      invalid_query_operator: クエリ演算子は "AND" または "OR" でなければなりません。
      invalid_value: 無効な値です。%{attribute_name} に提供された値は無効です。
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: レポート期間 %{since} から %{until} まで
    utc_warning: 生成されたレポートはUTCタイムゾーンです
    agent_csv:
      agent_name: 担当者名
      conversations_count: 割り当て済みの会話
      avg_first_response_time: 初回応答の平均時間
      avg_resolution_time: 解決までの平均時間
      resolution_count: 処理件数
      avg_customer_waiting_time: お客様の平均待ち時間
    inbox_csv:
      inbox_name: 受信トレイ名
      inbox_type: 受信トレイの種類
      conversations_count: 会話数
      avg_first_response_time: 初回応答の平均時間
      avg_resolution_time: 解決までの平均時間
    label_csv:
      label_title: ラベル
      conversations_count: 会話数
      avg_first_response_time: 初回応答の平均時間
      avg_resolution_time: 解決までの平均時間
      avg_reply_time: Avg reply time
      resolution_count: 処理件数
    team_csv:
      team_name: チーム名
      conversations_count: 会話回数
      avg_first_response_time: 初回応答の平均時間
      avg_resolution_time: 解決までの平均時間
      resolution_count: 処理件数
      avg_customer_waiting_time: お客様の平均待ち時間
    conversation_traffic_csv:
      timezone: タイムゾーン
    sla_csv:
      conversation_id: 会話ID
      sla_policy_breached: SLAポリシー
      assignee: 担当者
      team: チーム
      inbox: 受信トレイ
      labels: ラベル
      conversation_link: 会話へのリンク
      breached_events: 違反イベント
    default_group_by: 日
    csat:
      headers:
        contact_name: 連絡先名
        contact_email_address: 連絡先のメールアドレス
        contact_phone_number: 連絡先の電話番号
        link_to_the_conversation: 会話へのリンク
        agent_name: 担当者名
        rating: 評価
        feedback: フィードバックコメント
        recorded_at: 記録日
  notifications:
    notification_title:
      conversation_creation: '会話 (#%{display_id}) が %{inbox_name} で作成されました'
      conversation_assignment: '会話 (#%{display_id}) があなたに割り当てられました'
      assigned_conversation_new_message: '会話 (#%{display_id}) に新しいメッセージが作成されました'
      conversation_mention: '会話 (#%{display_id}) であなたが言及されました'
      sla_missed_first_response: '会話 (#%{display_id}) の最初の応答でSLAターゲットを逃しました'
      sla_missed_next_response: '会話 (#%{display_id}) の次の応答でSLAターゲットを逃しました'
      sla_missed_resolution: '会話 (#%{display_id}) の解決でSLAターゲットを逃しました'
    attachment: '添付ファイル'
    no_content: 'コンテンツなし'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} さんがストーリーであなたについて言及しました: '
      instagram_deleted_story_content: このストーリーはもう利用できません。
      deleted: このメッセージは削除されました
      whatsapp:
        list_button_label: 'Choose an item'
      delivery_status:
        error_code: 'エラーコード: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: '%{user_name} によって会話は解決済みになりました'
        contact_resolved: '%{contact_name} によって会話が解決されました'
        open: '%{user_name} によって会話が再開されました'
        pending: '%{user_name} によって会話が保留にされました'
        snoozed: '%{user_name} によって会話がスヌーズされました'
        auto_resolved_days: '%{count} 日間会話が非アクティブだったため、システムにより解決済みにされました'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: 新しい受信メッセージを受信したため、システムが会話が再開されました
      priority:
        added: '%{user_name} が優先度を "%{new_priority}" に設定しました'
        updated: '%{user_name} が優先度を "%{old_priority}" から "%{new_priority}" に変更しました'
        removed: '%{user_name} が優先度を削除しました'
      assignee:
        self_assigned: '%{user_name} がこの会話を自身に割り当てました'
        assigned: '%{user_name} によって %{assignee_name} に割り当てられました'
        removed: '%{user_name} によって割り当てが解除されました'
      team:
        assigned: '%{user_name} によって %{team_name} に割り当てられました'
        assigned_with_assignee: '%{user_name} によって %{team_name} 経由で %{assignee_name} に割り当てられました'
        removed: '%{user_name} によって %{team_name} から割り当てが解除されました'
      labels:
        added: '%{user_name} がラベル "%{labels}" を追加しました'
        removed: '%{user_name} がラベル "%{labels}" を削除しました'
      sla:
        added: '%{user_name} がSLAポリシー "%{sla_name}" を追加しました'
        removed: '%{user_name} がSLAポリシー "%{sla_name}" を削除しました'
      linear:
        issue_created: 'Linear issue %{issue_id} was created by %{user_name}'
        issue_linked: 'Linear issue %{issue_id} was linked by %{user_name}'
        issue_unlinked: 'Linear issue %{issue_id} was unlinked by %{user_name}'
      csat:
        not_sent_due_to_messaging_window: 'CSAT survey not sent due to outgoing message restrictions'
      auto_resolve:
        not_sent_due_to_messaging_window: 'Auto-resolve message not sent due to outgoing message restrictions'
      muted: '%{user_name} が会話をミュートしました'
      unmuted: '%{user_name} が会話のミュートを解除しました'
      auto_resolution_message: '会話がしばらく非アクティブのため、解決済みとして処理します。引き続きサポートが必要な場合は、新しい会話を開始してください。'
    templates:
      greeting_message_body: '%{account_name} は通常数時間以内に返信します。'
      ways_to_reach_you_message_body: 'ご連絡先をご入力ください。'
      email_input_box_message_body: '通知先Eメールアドレス'
      csat_input_message_body: '会話を評価してください'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} from %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} from %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} from %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} from %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'この会話に新着メッセージがあります'
      transcript_subject: '会話の記録'
    survey:
      response: 'この会話を評価してください, %{link}'
  contacts:
    online:
      delete: '%{contact_name} はオンラインです。後でもう一度お試しください'
  integration_apps:
    #Note: webhooks and dashboard_apps don't need short_description as they use different modal components
    dashboard_apps:
      name: 'ダッシュボードアプリ'
      description: 'ダッシュボードアプリを使用すると、ユーザー情報、注文、または支払い履歴を表示するアプリケーションを作成して埋め込むことができ、カスタマーサポートエージェントにより多くのコンテキストを提供します。'
    dyte:
      name: 'Dyte'
      short_description: 'Start video/voice calls with customers directly from Chatwoot.'
      description: 'Dyteは、オーディオおよびビデオ機能をアプリケーションに統合する製品です。この統合により、エージェントはChatwootから直接顧客とビデオ/音声通話を開始できます。'
      meeting_name: '%{agent_name} がミーティングを開始しました'
    slack:
      name: 'Slack'
      short_description: 'Receive notifications and respond to conversations directly in Slack.'
      description: "ChatwootとSlackを統合して、チームを同期させます。この統合により、新しい会話の通知を受け取り、Slackのインターフェース内で直接応答することができます。"
    webhooks:
      name: 'Webhooks'
      description: 'Webhookイベントは、Chatwootアカウント内のアクティビティに関するリアルタイムの更新を提供します。希望するイベントを購読すると、Chatwootは更新情報を含むHTTPコールバックを送信します。'
    dialogflow:
      name: 'Dialogflow'
      short_description: 'Build chatbots to handle initial queries before transferring to agents.'
      description: 'Dialogflowでチャットボットを構築し、それらを受信トレイに簡単に統合します。これらのボットは、カスタマーサービスエージェントに転送する前に初期の問い合わせを処理できます。'
    google_translate:
      name: 'Google Translate'
      short_description: 'Automatically translate customer messages for agents.'
      description: "Google翻訳を統合して、エージェントが顧客のメッセージを簡単に翻訳できるようにします。この統合は言語を自動的に検出し、エージェントまたは管理者の希望する言語に変換します。"
    openai:
      name: 'OpenAI'
      short_description: 'AI-powered reply suggestions, summarization, and message enhancement.'
      description: 'OpenAIの大規模言語モデルの力を活用して、返信の提案、要約、メッセージの言い換え、スペルチェック、ラベル分類などの機能を提供します。'
    linear:
      name: 'Linear'
      short_description: 'Create and link Linear issues directly from conversations.'
      description: '会話ウィンドウから直接Linearに問題を作成します。あるいは、既存のLinearの問題をリンクして、より効率的な問題追跡プロセスを実現します。'
    notion:
      name: 'Notion'
      short_description: 'Integrate databases, documents and pages directly with Captain.'
      description: 'Connect your Notion workspace to enable Captain to access and generate intelligent responses using content from your databases, documents, and pages to provide more contextual customer support.'
    shopify:
      name: 'Shopify'
      short_description: 'Access order details and customer data from your Shopify store.'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_message_required: メッセージは必須です
    copilot_error: 'この受信トレイにアシスタントを接続してCopilotを使用してください'
    copilot_limit: 'Copilot残高がありません。課金セクションからクレジットを追加購入することができます。'
    copilot:
      using_tool: 'Using tool %{function_name}'
      completed_tool_call: 'Completed %{function_name} tool call'
      invalid_tool_call: 'Invalid tool call'
      tool_not_available: 'Tool not available'
  public_portal:
    search:
      search_placeholder: タイトルまたは本文で記事を検索...
      empty_placeholder: 該当結果が見つかりませんでした。
      loading_placeholder: 検索中...
      results_title: 検索結果
    toc_header: 'このページで'
    hero:
      sub_title: ここで記事を検索するか、以下のカテゴリを参照してください。
    common:
      home: ホーム
      last_updated_on: 最終更新日 %{last_updated_on}
      view_all_articles: すべて表示
      article: 記事
      articles: 記事
      author: 著者
      authors: 著者
      other: その他
      others: その他
      by: 作成者
      no_articles: ここには記事がありません
    footer:
      made_with: 作成
    header:
      go_to_homepage: ウェブサイト
      visit_website: Visit website
      appearance:
        system: システム
        light: ライト
        dark: ダーク
      featured_articles: 注目の記事
      uncategorized: 未分類
    404:
      title: ページが見つかりません
      description: お探しのページが見つかりませんでした。
      back_to_home: ホームページに戻る
  slack_unfurl:
    fields:
      name: 名前
      email: Eメール
      phone_number: 電話
      company_name: 企業
      inbox_name: 受信トレイ
      inbox_type: 受信トレイの種類
    button: 会話を開く
  time_units:
    days:
      other: '%{count} 日'
    hours:
      other: '%{count} 時間'
    minutes:
      other: '%{count} 分'
    seconds:
      other: '%{count} 秒'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[コンテンツなし]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
