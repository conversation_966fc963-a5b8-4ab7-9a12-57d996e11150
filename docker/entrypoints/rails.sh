#!/bin/sh

set -x

# Remove a potentially pre-existing server.pid for Rails.
rm -rf /app/tmp/pids/server.pid
rm -rf /app/tmp/cache/*

if [ "$SKIP_DB_SETUP" != "true" ]; then
  echo "Waiting for postgres to become ready...."

  $(docker/entrypoints/helpers/pg_database_url.rb)
  PG_READY="pg_isready -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USERNAME"

  until $PG_READY
  do
    sleep 2;
  done

  echo "Database ready to accept connections."
else
  echo "Skipping database readiness check - using external database"
fi

#install missing gems for local dev as we are using base image compiled for production
bundle install

BUNDLE="bundle check"

until $BUNDLE
do
  sleep 2;
done

# Execute the main process of the container
exec "$@"
