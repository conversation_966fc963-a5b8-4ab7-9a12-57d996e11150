{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON><PERSON>", "LOADING_EDITOR": "Carregando Editor...", "DESCRIPTION": "Robôs agentes são como os membros mais fabulosos de seu time. Eles podem lidar com as pequenas coisas, assim você pode focar nas coisas que importam. Dê uma chance a eles. Você pode gerenciar seus robôs a partir desta página ou criar novos usando o botão 'Criar Robô'.", "LEARN_MORE": "Aprenda sobre os robôs agentes", "GLOBAL_BOT": "Robô do sistema", "GLOBAL_BOT_BADGE": "Sistema", "AVATAR": {"SUCCESS_DELETE": "Avatar do robô excluído com sucesso", "ERROR_DELETE": "Erro ao excluir o avatar do robô, tente novamente"}, "BOT_CONFIGURATION": {"TITLE": "Selecione um robô de agente", "DESC": "Atribua um Agente Robô à sua caixa de entrada. Eles podem lidar com as conversas iniciais e transferi-las para um agente humano quando necessário.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "Desconectar Robô", "SUCCESS_MESSAGE": "Agente de bot atualizado com sucesso.", "DISCONNECTED_SUCCESS_MESSAGE": "Bot desconectado com sucesso.", "ERROR_MESSAGE": "Não foi possível atualizar o agente robô. Por favor, tente novamente mais tarde.", "DISCONNECTED_ERROR_MESSAGE": "Não foi possível desconectar o agente robô. Por favor, tente novamente mais tarde.", "SELECT_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>"}, "ADD": {"TITLE": "<PERSON><PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Bot adicionado com sucesso.", "ERROR_MESSAGE": "Não foi possível adicionar o agente robô! Por favor, tente novamente mais tarde."}}, "LIST": {"404": "Nenhum robô encontrado. Você pode criar um robô clicando no botão 'Criar Robô'.", "LOADING": "Bus<PERSON><PERSON> rob<PERSON>...", "TABLE_HEADER": {"DETAILS": "<PERSON><PERSON><PERSON>", "URL": "URL do Webhook"}}, "DELETE": {"BUTTON_TEXT": "Excluir", "TITLE": "<PERSON><PERSON><PERSON> rob<PERSON>", "CONFIRM": {"TITLE": "Confirmar exclusão", "MESSAGE": "Tem certeza que deseja excluir {name}?", "YES": "Sim, excluir", "NO": "Não, Mantenha"}, "API": {"SUCCESS_MESSAGE": "Bot excluído com sucesso.", "ERROR_MESSAGE": "Não foi possível excluir o robô. Por favor, tente novamente."}}, "EDIT": {"BUTTON_TEXT": "Alterar", "TITLE": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Robô atualizado com sucesso.", "ERROR_MESSAGE": "Não foi possível atualizar o robô. Por favor, tente novamente mais tarde."}}, "ACCESS_TOKEN": {"TITLE": "<PERSON><PERSON> de acesso", "DESCRIPTION": "Copie o token de acesso e salve-o de forma segura", "COPY_SUCCESSFUL": "Token de acesso copiado para área de transferência", "RESET_SUCCESS": "Token de acesso gerado novamente com sucesso", "RESET_ERROR": "Não foi possível regerar o token de acesso. Por favor, tente novamente"}, "FORM": {"AVATAR": {"LABEL": "Avatar do robô"}, "NAME": {"LABEL": "Nome do Robô", "PLACEHOLDER": "Insira o nome do robô", "REQUIRED": "O nome do Robô é obrigatório"}, "DESCRIPTION": {"LABEL": "Descrição", "PLACEHOLDER": "O que esse robô faz?"}, "WEBHOOK_URL": {"LABEL": "URL do Webhook", "PLACEHOLDER": "https://exemplo.com.br/webhook", "REQUIRED": "URL Webhook é necessária"}, "ERRORS": {"NAME": "O nome do Robô é obrigatório", "URL": "URL Webhook é necessária", "VALID_URL": "Digite uma URL válida começando com http:// ou https://"}, "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "<PERSON><PERSON><PERSON> <PERSON>", "UPDATE": "Atualizar o <PERSON>"}, "WEBHOOK": {"DESCRIPTION": "Configure um robô de Webhook para integrar com seus serviços personalizados. O robô receberá e processará eventos de conversas e pode respondê-los."}, "TYPES": {"WEBHOOK": "Webhook robô"}}}