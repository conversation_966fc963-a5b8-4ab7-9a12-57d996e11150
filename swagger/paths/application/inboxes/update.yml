patch:
  tags:
    - Inboxes
  operationId: updateInbox
  summary: Update Inbox
  security:
    - userApiKey: []
  description: Update an existing inbox
  parameters:
    - $ref: '#/components/parameters/account_id'
    - name: id
      in: path
      schema:
        type: number
      description: ID of the inbox
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '#/components/schemas/inbox_update_payload'
  responses:
    '200':
      description: Success
      content:
        application/json:
          schema:
            type: object
            description: 'Updated inbox object'
            $ref: '#/components/schemas/inbox'
    '404':
      description: Inbox not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
    '403':
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/bad_request_error'
