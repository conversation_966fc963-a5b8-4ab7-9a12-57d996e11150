# Rails Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-rails
  namespace: chat-prod
spec:
  replicas: 2
  selector:
    matchLabels:
      app: chat-rails
  template:
    metadata:
      labels:
        app: chat-rails
    spec:
      containers:
      - name: chat-rails
        image: chatwoot/chatwoot:latest
        ports:
        - containerPort: 3000
        command: ['bundle', 'exec', 'rails', 's', '-p', '3000', '-b', '0.0.0.0']
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: NODE_ENV
        - name: RAILS_ENV
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: RAILS_ENV
        - name: INSTALLATION_ENV
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: INSTALLATION_ENV
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_HOST
        - name: POSTGRES_USERNAME
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_USERNAME
        - name: POSTGRES_DATABASE
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_DATABASE
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chat-secrets
              key: POSTGRES_PASSWORD
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: REDIS_URL
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chat-secrets
              key: REDIS_PASSWORD
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: chat-secrets
              key: SECRET_KEY_BASE
        - name: FRONTEND_URL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: FRONTEND_URL
        - name: FORCE_SSL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: FORCE_SSL
        - name: ENABLE_ACCOUNT_SIGNUP
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: ENABLE_ACCOUNT_SIGNUP
        - name: RAILS_LOG_TO_STDOUT
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: RAILS_LOG_TO_STDOUT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: LOG_LEVEL
        - name: RAILS_MAX_THREADS
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: RAILS_MAX_THREADS
        - name: ACTIVE_STORAGE_SERVICE
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: ACTIVE_STORAGE_SERVICE
        - name: MAILER_SENDER_EMAIL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: MAILER_SENDER_EMAIL
        - name: SMTP_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: SMTP_DOMAIN
        - name: ENABLE_PUSH_RELAY_SERVER
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: ENABLE_PUSH_RELAY_SERVER
        volumeMounts:
        - name: storage-volume
          mountPath: /app/storage
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: storage-volume
        persistentVolumeClaim:
          claimName: chat-storage-pvc
      restartPolicy: Always
---
# Rails Service
apiVersion: v1
kind: Service
metadata:
  name: chat-rails
  namespace: chat-prod
spec:
  selector:
    app: chat-rails
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP
---
# LoadBalancer Service for external access
apiVersion: v1
kind: Service
metadata:
  name: chat-web
  namespace: chat-prod
spec:
  selector:
    app: chat-rails
  ports:
    - port: 80
      targetPort: 3000
      protocol: TCP
  type: LoadBalancer
