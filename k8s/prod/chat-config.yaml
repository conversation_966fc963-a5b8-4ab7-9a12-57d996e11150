# ConfigMap for environment variables
apiVersion: v1
kind: ConfigMap
metadata:
  name: chat-config
  namespace: chat-prod
data:
  NODE_ENV: "production"
  RAILS_ENV: "production"
  INSTALLATION_ENV: "docker"
  POSTGRES_HOST: "chatwoot.cluster-clg02agyig7g.ap-east-1.rds.amazonaws.com"
  POSTGRES_USERNAME: "postgres"
  POSTGRES_PASSWORD: "g5q1AAW29XqhMKGs2yLw"
  POSTGRES_DATABASE: "chat"
  REDIS_URL: "redis://chat-redis.chat-prod:6379"
  REDIS_PASSWORD: "4kbWFZ4AyyaCjBKAP0hz"
  FRONTEND_URL: "http://0.0.0.0:3000"
  FORCE_SSL: "false"
  ENABLE_ACCOUNT_SIGNUP: "false"
  RAILS_LOG_TO_STDOUT: "true"
  LOG_LEVEL: "info"
  LOG_SIZE: "500"
  RAILS_MAX_THREADS: "5"
  ACTIVE_STORAGE_SERVICE: "local"
  MAILER_SENDER_EMAIL: "Chatwoot <<EMAIL>>"
  SMTP_DOMAIN: "chatwoot.com"
  SMTP_PORT: "1025"
  SMTP_ENABLE_STARTTLS_AUTO: "true"
  SMTP_OPENSSL_VERIFY_MODE: "peer"
  ENABLE_PUSH_RELAY_SERVER: "true"
  SKIP_DB_SETUP: "true"
  SKIP_DB_MIGRATIONS: "true"
  SECRET_KEY_BASE: "replace_with_lengthy_secure_hex"
---
# PersistentVolumeClaim for storage data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chat-storage-pvc
  namespace: chat-prod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
