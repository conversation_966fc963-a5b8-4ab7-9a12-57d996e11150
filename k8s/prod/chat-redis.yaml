apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-redis
  namespace: chat-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chat-redis
  template:
    metadata:
      labels:
        app: chat-redis
    spec:
      containers:
        - name: redis
          image: redis:latest
          command: ["redis-server","/usr/local/etc/redis/redis.conf"]
          ports:
            - containerPort: 6379
          volumeMounts:
            - name: chat-redis-config
              mountPath: /usr/local/etc/redis/redis.conf
              subPath: redis.conf
            - name: chat-redis-storage
              mountPath: /data
      volumes:
        - name: chat-redis-config
          configMap:
            name: chat-redis-config
        - name: chat-redis-storage
          persistentVolumeClaim:
            claimName: chat-redis-data
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: chat-redis
  name: chat-redis
  namespace: chat-prod
spec:
  ports:
    - name: http
      port: 6379
      protocol: TCP
      targetPort: 6379
  selector:
    app: chat-redis
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: chat-redis-config
  namespace: chat-prod
data:
  redis.conf: |
    protected-mode no
    bind 0.0.0.0
    port 6379
    requirepass 4kbWFZ4AyyaCjBKAP0hz
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chat-redis-data
  namespace: chat-prod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
