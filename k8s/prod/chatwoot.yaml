apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: atg-apiend
  name: atg-apiend
  namespace: atg-prod
spec:
  replicas: 2
  selector:
    matchLabels:
      app: atg-apiend
  strategy: {}
  template:
    metadata:
      labels:
        app: atg-apiend
    spec:
      nodeSelector:
        eks.amazonaws.com/nodegroup: main
      containers:
        - image: 527135721797.dkr.ecr.ap-east-1.amazonaws.com/atg-prod-hk/apiend:1.0.0
          name: apiend
          env:
            - name: MYSQL_URL
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: mysql_url
            - name: MYSQL_USER
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: mysql_user
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: mysql_password
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: redis_url
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: redis_password
            - name: ES_URL
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: es_url
            - name: ES_USER
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: es_user
            - name: ES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: es_password
            - name: EMAIL_USER
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: email_user
            - name: EMAIL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: email_password
            - name: GOOGLE_AUTH_USER
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: google_auth_user
            - name: GOOGLE_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: google_auth_client_id
            - name: GOOGLE_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: google_auth_client_secret
            - name: FACEBOOK_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: facebook_auth_client_id
            - name: FACEBOOK_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: facebook_auth_client_secret
            - name: TURNSTILE_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: turnstile_secret_key
            - name: TURNSTILE_SITE_KEY
              valueFrom:
                secretKeyRef:
                  name: atg-application
                  key: turnstile_site_key
          resources: {}
          volumeMounts:
            - name: app-logs
              mountPath: /var/log
        - image: docker.elastic.co/beats/filebeat:8.14.3
          name: filebeat
          volumeMounts:
            - name: atg-apiend-filebeat-config
              mountPath: /usr/share/filebeat/filebeat.yml
              subPath: filebeat.yml
            - name: app-logs
              mountPath: /var/log
              readOnly: true
      volumes:
        - name: app-logs
          emptyDir: {}
        - name: atg-apiend-filebeat-config
          configMap:
            name: atg-apiend-filebeat-config
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: atg-apiend
  name: atg-apiend
  namespace: atg-prod
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8081
  selector:
    app: atg-apiend
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: atg-apiend-filebeat-config
  namespace: atg-prod
data:
  filebeat.yml: |
    filebeat.inputs:
      - type: filestream
        id: atg-prod-apiend
        enabled: true
        fields_under_root: true
        fields:
          project: atg
          app: atg-prod-apiend
        paths:
          - "/var/log/app.log"
    filebeat.config.modules:
      path: ${path.config}/modules.d/*.yml
      reload.enabled: false
    output.elasticsearch:
      hosts: ["************:9200"]
      username: "elastic"
      password: "WxVheAuXk99eqRAZgZYP"
      index: "atg-prod-apiend-%{+yyyy.MM}"
    setup.template.name: "atg-prod-apiend"
    setup.template.pattern: "atg-prod-apiend-*"
    processors:
      - add_host_metadata:
          when.not.contains.tags: forwarded
      - add_cloud_metadata: ~
      - add_docker_metadata: ~
      - add_kubernetes_metadata: ~