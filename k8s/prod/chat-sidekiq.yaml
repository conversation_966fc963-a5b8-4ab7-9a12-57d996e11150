# Sidekiq Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-sidekiq
  namespace: chat-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chat-sidekiq
  template:
    metadata:
      labels:
        app: chat-sidekiq
    spec:
      containers:
      - name: chat-sidekiq
        image: chatwoot/chatwoot:latest
        command: ['bundle', 'exec', 'sidekiq', '-C', 'config/sidekiq.yml']
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: NODE_ENV
        - name: RAILS_ENV
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: RAILS_ENV
        - name: INSTALLATION_ENV
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: INSTALLATION_ENV
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_HOST
        - name: POSTGRES_USERNAME
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_USERNAME
        - name: POSTGRES_DATABASE
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_DATABASE
        - name: POSTGRES_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: POSTGRES_PASSWORD
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: REDIS_URL
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chat-secrets
              key: REDIS_PASSWORD
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: chat-secrets
              key: SECRET_KEY_BASE
        - name: RAILS_LOG_TO_STDOUT
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: RAILS_LOG_TO_STDOUT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: LOG_LEVEL
        - name: RAILS_MAX_THREADS
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: RAILS_MAX_THREADS
        - name: ACTIVE_STORAGE_SERVICE
          valueFrom:
            configMapKeyRef:
              name: chat-config
              key: ACTIVE_STORAGE_SERVICE
        volumeMounts:
        - name: storage-volume
          mountPath: /app/storage
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: storage-volume
        persistentVolumeClaim:
          claimName: chat-storage-pvc
      restartPolicy: Always
